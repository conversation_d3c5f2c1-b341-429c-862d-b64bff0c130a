-- Add performance indexes for thumbnail queries
-- These indexes will significantly improve query performance for user-specific thumbnail fetching

-- Index for generated_thumbnails table
CREATE INDEX IF NOT EXISTS "generated_thumbnails_userId_idx" ON "generated_thumbnails" ("userId");
CREATE INDEX IF NOT EXISTS "generated_thumbnails_createdAt_idx" ON "generated_thumbnails" ("createdAt");
CREATE INDEX IF NOT EXISTS "generated_thumbnails_userId_createdAt_idx" ON "generated_thumbnails" ("userId", "createdAt");

-- Index for recreated_thumbnails table  
CREATE INDEX IF NOT EXISTS "recreated_thumbnails_userId_idx" ON "recreated_thumbnails" ("userId");
CREATE INDEX IF NOT EXISTS "recreated_thumbnails_createdAt_idx" ON "recreated_thumbnails" ("createdAt");
CREATE INDEX IF NOT EXISTS "recreated_thumbnails_userId_createdAt_idx" ON "recreated_thumbnails" ("userId", "createdAt");
CREATE INDEX IF NOT EXISTS "recreated_thumbnails_sourceType_idx" ON "recreated_thumbnails" ("sourceType");
CREATE INDEX IF NOT EXISTS "recreated_thumbnails_personaUsed_idx" ON "recreated_thumbnails" ("personaUsed");

-- Index for faceswap_thumbnails table
CREATE INDEX IF NOT EXISTS "faceswap_thumbnails_userId_idx" ON "faceswap_thumbnails" ("userId");
CREATE INDEX IF NOT EXISTS "faceswap_thumbnails_createdAt_idx" ON "faceswap_thumbnails" ("createdAt");
CREATE INDEX IF NOT EXISTS "faceswap_thumbnails_userId_createdAt_idx" ON "faceswap_thumbnails" ("userId", "createdAt");

-- Index for users table (if not already exists)
CREATE INDEX IF NOT EXISTS "users_userId_idx" ON "users" ("userId");
