import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/src/lib/auth";
import {
  getUserFaceSwapThumbnails,
  getFaceSwapThumbnail,
  deleteFaceSwapThumbnail,
} from "@/src/actions/faceSwapAction";

export const dynamic = "force-dynamic";

export async function GET(req: NextRequest) {
  try {
    // Authenticate user
    const { supabaseUser } = await getCurrentUser();
    const userId = supabaseUser.id;

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";

    // Use Drizzle action for paginated thumbnails
    const { thumbnails, pagination } = await getUserFaceSwapThumbnails(userId, {
      page,
      limit,
      sortBy,
      sortOrder,
    });

    return NextResponse.json(
      {
        success: true,
        data: {
          thumbnails,
          pagination,
        },
      },
      {
        headers: {
          "Cache-Control": "private, max-age=30, stale-while-revalidate=60",
          Vary: "Authorization",
        },
      }
    );
  } catch (error) {
    console.error("Error fetching faceswap thumbnails:", error);
    return NextResponse.json(
      { success: false, error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // Authenticate user
    const { supabaseUser } = await getCurrentUser();
    const userId = supabaseUser.id;

    const { searchParams } = new URL(req.url);
    const thumbnailId = searchParams.get("id");

    if (!thumbnailId) {
      return NextResponse.json(
        { success: false, error: "Thumbnail ID is required" },
        { status: 400 }
      );
    }

    // Check if thumbnail exists and belongs to user
    const thumbnail = await getFaceSwapThumbnail(thumbnailId);
    if (!thumbnail || thumbnail.userId !== userId) {
      return NextResponse.json(
        { success: false, error: "Thumbnail not found" },
        { status: 404 }
      );
    }

    // Delete thumbnail from database
    await deleteFaceSwapThumbnail(thumbnailId);

    return NextResponse.json({
      success: true,
      message: "Thumbnail deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting faceswap thumbnail:", error);
    return NextResponse.json(
      { success: false, error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
