import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/src/lib/auth";
import { getUserGeneratedThumbnails } from "@/src/actions/generatedThumbnailAction";

export async function GET(req: NextRequest) {
  try {
    // Get authenticated user
    const { supabaseUser: user } = await getCurrentUser();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";

    // Use Drizzle action for paginated thumbnails
    const { thumbnails, pagination } = await getUserGeneratedThumbnails(
      user.id,
      {
        page,
        limit,
        search,
        sortBy,
        sortOrder,
      }
    );

    return NextResponse.json(
      {
        success: true,
        data: {
          thumbnails,
          pagination,
        },
      },
      {
        headers: {
          "Cache-Control": "private, max-age=30, stale-while-revalidate=60",
          Vary: "Authorization",
        },
      }
    );
  } catch (error) {
    console.error("Error fetching thumbnails:", error);

    // Check if it's an authentication error
    if (error instanceof Error && error.message === "Unauthorized") {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to fetch thumbnails" },
      { status: 500 }
    );
  }
}
