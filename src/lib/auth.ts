import { createClient } from "@/src/lib/supabase/server";
import { getUser, updateUser, createUser } from "@/src/actions/userAction";
import { User as SupabaseUser } from "@supabase/supabase-js";

/**
 * Gets the current user from Supabase auth and syncs with our database
 * Returns both the Supabase user and our database user
 * Optimized to minimize database calls
 */
export async function getCurrentUser(): Promise<{
  supabaseUser: SupabaseUser;
  dbUser: ReturnType<typeof getUser> extends Promise<infer T> ? T : never;
}> {
  const supabase = await createClient();

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      throw new Error("Unauthorized");
    }

    // Get user from database - this is the only required DB call
    let dbUser = await getUser(user.id);

    // If user doesn't exist in our database, create them
    if (!dbUser) {
      const name =
        user.user_metadata?.full_name || user.user_metadata?.name || null;
      const avatarUrl = user.user_metadata?.avatar_url || null;

      dbUser = await createUser({
        userId: user.id,
        email: user.email!,
        name,
        avatarUrl,
      });
    }
    // Only update if data has actually changed (avoid unnecessary writes)
    else {
      const name =
        user.user_metadata?.full_name || user.user_metadata?.name || null;
      const avatarUrl = user.user_metadata?.avatar_url || null;

      if (
        dbUser.email !== user.email ||
        dbUser.name !== name ||
        dbUser.avatarUrl !== avatarUrl
      ) {
        dbUser = await updateUser(user.id, { name, avatarUrl });
      }
    }

    return { supabaseUser: user, dbUser };
  } catch (error) {
    console.error("Error getting current user:", error);
    throw error;
  }
}

export async function getCurrentUserId(): Promise<string> {
  const { supabaseUser } = await getCurrentUser();
  return supabaseUser.id;
}
