import { pgTable, text, timestamp, boolean, index } from "drizzle-orm/pg-core";
import { createId } from "@paralleldrive/cuid2";

// Users table
export const users = pgTable("users", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => createId()),
  userId: text("userId").notNull().unique(),
  email: text("email").notNull().unique(),
  name: text("name"),
  avatarUrl: text("avatarUrl"),
  createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

// Generated Thumbnails table
export const generatedThumbnails = pgTable(
  "generated_thumbnails",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    prompt: text("prompt").notNull(),
    imageUrl: text("imageUrl").notNull(),
    title: text("title"),
    tags: text("tags").array().notNull().default([]),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("generated_thumbnails_userId_idx").on(table.userId),
    createdAtIdx: index("generated_thumbnails_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("generated_thumbnails_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
  })
);

// Recreated Thumbnails table
export const recreatedThumbnails = pgTable(
  "recreated_thumbnails",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    imageUrl: text("imageUrl").notNull(),
    sourceType: text("sourceType").notNull(),
    prompt: text("prompt").notNull(),
    personaUsed: boolean("personaUsed").notNull().default(false),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("recreated_thumbnails_userId_idx").on(table.userId),
    createdAtIdx: index("recreated_thumbnails_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("recreated_thumbnails_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
    sourceTypeIdx: index("recreated_thumbnails_sourceType_idx").on(
      table.sourceType
    ),
    personaUsedIdx: index("recreated_thumbnails_personaUsed_idx").on(
      table.personaUsed
    ),
  })
);

// Face Swap Thumbnails table
export const faceSwapThumbnails = pgTable(
  "faceswap_thumbnails",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    imageUrl: text("imageUrl").notNull(),
    originalImageUrl: text("originalImageUrl").notNull(),
    personaImageUrl: text("personaImageUrl").notNull(),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("faceswap_thumbnails_userId_idx").on(table.userId),
    createdAtIdx: index("faceswap_thumbnails_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("faceswap_thumbnails_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
  })
);
